"use client";

import { Dispatch, SetStateAction, useEffect, useState, useRef, useCallback } from "react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Clock, Heart, Users, ArrowLeft, Volume2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { getCurrentWordForMatch, fetchPlayersFromDatabase, initiateSpellingTimer, performHeartbeatSyncBattle, updatePendingPlayers, updatePlayersHasAnswered, updateAllPlayersToPending, handleLeaveRoom } from "@/utils/battle-utils";
import { Player, User, Match, GameStateType, Difficulty, currentWord } from "@/interfaces/interfaces";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { createClient } from "../../supabase/client";
import React from "react";
import { getWordAudioPath, playWordAudio } from "@/utils/audio-utils";
import { setCurrentWordId, setMatchLock, updateMatchCurrentState } from "@/utils/database";
import { calculateTimeLeft, formatTime, resetStartTime } from "@/utils/waiting-utils";

interface BattlePageProps {
  roomName: string;
  roomColor: string;
  roomBgColor: string;
  matchId?: string;
  clientGameState: GameStateType;
  setClientGameState: Dispatch<SetStateAction<GameStateType>>;
  clientCurrentWordId: number;
  setClientCurrentWordId: Dispatch<SetStateAction<number>>;
  currentWord: currentWord;
  setCurrentWord: Dispatch<SetStateAction<currentWord>>;
  currentUser: User; // Add currentUser to props
}

export default function BattlePage({
  roomName,
  roomColor,
  roomBgColor,
  matchId,
  clientGameState,
  setClientGameState,
  clientCurrentWordId,
  setClientCurrentWordId,
  currentWord,
  setCurrentWord,
  currentUser
}: BattlePageProps) {
  const supabase = createClient();
  const router = useRouter();

  const isFirstMount = React.useRef(true);
  const hasInitialized = React.useRef(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const [isAudioPlaying, setIsAudioPlaying] = useState(true);
  const [userAnswer, setUserAnswer] = useState("");
  const [playerData, setPlayerData] = useState<Player[]>([]);
  const [countdownActive, setCountdownActive] = useState(false);
  const [initialTime, setInitialTime] = useState(15);
  const [correctAnswer, setCorrectAnswer] = useState("");
  const [timeLeft, setTimeLeft] = useState(15);
  const [breakTimerStart, setBreakTimerStart] = useState(false);
  const [recentEliminations, setRecentEliminations] = useState<string[]>([]);
  const [currentRound, setCurrentRound] = useState(1);
  const [timeLeftPrecise, setTimeLeftPrecise] = useState(15000); // Time in milliseconds for precise tracking
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Helper function to determine if current user is a spectator
  const isCurrentUserSpectator = () => {
    const currentPlayer = playerData.find(p => p.id === currentUser.id);
    return (currentPlayer?.lives || 0) <= 0 || currentPlayer?.is_spectator;
  };

  // Helper function to calculate round statistics
  const getRoundStats = () => {
    const activePlayers = playerData.filter(p => (p.lives || 0) > 0 && !p.is_spectator);
    const spectators = playerData.filter(p => (p.lives || 0) <= 0 || p.is_spectator);
    const correctAnswers = playerData.filter(p => p.lastAnswer === "correct").length;
    const incorrectAnswers = playerData.filter(p => p.lastAnswer === "incorrect").length;

    return {
      activePlayers: activePlayers.length,
      spectators: spectators.length,
      correctAnswers,
      incorrectAnswers,
      totalPlayers: playerData.length,
      highestScore: Math.max(...playerData.map(p => p.score || 0), 0),
      averageScore: playerData.length > 0 ? Math.round(playerData.reduce((sum, p) => sum + (p.score || 0), 0) / playerData.length) : 0
    };
  };

  // Calculate dynamic timer duration based on round number
  const calculateTimerDuration = (round: number): number => {
    // Start with 15 seconds, decrease by 0.2 seconds each round, minimum 5 seconds
    const baseDuration = 15;
    const decreasePerRound = 0.2;
    const minDuration = 2;

    const calculatedDuration = baseDuration - ((round - 1) * decreasePerRound);
    return Math.max(calculatedDuration, minDuration);
  };

  // Calculate score based on response time
  const calculateTimeBasedScore = (timeRemainingMs: number, totalTimeMs: number): number => {
    // Score range: 500-1000 points
    const minScore = 500;
    const maxScore = 1000;

    // Calculate percentage of time remaining (faster response = higher percentage)
    const timePercentage = timeRemainingMs / totalTimeMs;

    // Calculate score: faster responses get higher scores
    const score = minScore + (timePercentage * (maxScore - minScore));

    return Math.round(score);
  };

  // Test function to verify calculations (for development/debugging)
  const testCalculations = () => {
    console.log("=== Timer and Scoring Test Results ===");

    // Test timer duration calculations
    for (let round = 1; round <= 10; round++) {
      const duration = calculateTimerDuration(round);
      console.log(`Round ${round}: ${duration}s timer`);
    }

    // Test scoring calculations
    console.log("\n=== Scoring Examples (15s timer) ===");
    const testScores = [
      { timeLeft: 15000, total: 15000, description: "Answered immediately" },
      { timeLeft: 10000, total: 15000, description: "Answered at 10s remaining" },
      { timeLeft: 5000, total: 15000, description: "Answered at 5s remaining" },
      { timeLeft: 1000, total: 15000, description: "Answered at 1s remaining" },
      { timeLeft: 0, total: 15000, description: "Answered at last moment" }
    ];

    testScores.forEach(test => {
      const score = calculateTimeBasedScore(test.timeLeft, test.total);
      const percentage = ((test.timeLeft / test.total) * 100).toFixed(1);
      console.log(`${test.description}: ${score} points (${percentage}% time remaining)`);
    });
  };

  const handleCheckAnswer = async () => {
    // Prevent spectators from submitting answers
    const currentPlayer = playerData.find(p => p.id === currentUser.id);
    const isSpectator = (currentPlayer?.lives || 0) <= 0 || currentPlayer?.is_spectator;

    if (isSpectator) {
      toast.error("Spectators cannot submit answers.");
      return;
    }

    const isCorrect = userAnswer.trim().toLowerCase() === correctAnswer.toLowerCase();

    if (isCorrect) {
      if (!currentPlayer) {
        toast.error("Player not found.");
        return;
      }

      let updatedScore = currentPlayer.score || 0;
      let updatedLives = currentPlayer.lives || 0;
      let lastAnswerStatus = "";

      // Calculate time-based score
      const totalTimeMs = calculateTimerDuration(currentRound) * 1000;
      const timeRemainingMs = timeLeftPrecise;
      const earnedPoints = calculateTimeBasedScore(timeRemainingMs, totalTimeMs);

      updatedScore += earnedPoints;
      lastAnswerStatus = "correct";

      toast.success(`Correct! +${earnedPoints} points!`);

      // Update player in database
      const { error: updateError } = await supabase
        .from('match_players')
        .update({
          score: updatedScore,
          lives: updatedLives,
          lastAnswer: lastAnswerStatus,
          has_answered: true // Mark player as having answered
        })
        .eq('player_id', currentUser.id)
        .eq('match_id', matchId);

      if (updateError) {
        console.error("Error updating player data:", updateError);
        toast.error("Failed to update player data.");
        return;
      }

      // Update local state
      setPlayerData(prevData =>
        prevData.map(p =>
          p.id === currentUser.id
            ? { ...p, score: updatedScore, lives: updatedLives, lastAnswer: lastAnswerStatus, has_answered: true }
            : p
        )
      );

      setUserAnswer("");
      setClientGameState("break");
    } else {
      toast.error("Incorrect answer. Try again!");

      if (!currentPlayer) {
        toast.error("Player not found.");
        return;
      }

      let updatedScore = currentPlayer.score || 0;
      let updatedLives = currentPlayer.lives || 0;
      let lastAnswerStatus = "";

      updatedLives = Math.max(0, updatedLives - 1); // Decrease lives by 1, minimum 0
      lastAnswerStatus = "incorrect";

      // Update player in database
      const { error: updateError } = await supabase
        .from('match_players')
        .update({
          score: updatedScore,
          lives: updatedLives,
          lastAnswer: lastAnswerStatus,
          has_answered: true // Mark player as having answered
        })
        .eq('player_id', currentUser.id)
        .eq('match_id', matchId);

      if (updateError) {
        console.error("Error updating player data:", updateError);
        toast.error("Failed to update player data.");
        return;
      }

      // Update local state
      setPlayerData(prevData =>
        prevData.map(p =>
          p.id === currentUser.id
            ? { ...p, score: updatedScore, lives: updatedLives, lastAnswer: lastAnswerStatus, has_answered: true }
            : p
        )
      );

      setUserAnswer("");
      setClientGameState("break");
    }
  }

  const fetchPlayers = async () => {
    if (matchId) {
      const fetchedPlayers = await fetchPlayersFromDatabase(roomName, matchId);
      console.log(fetchedPlayers);
      setPlayerData(fetchedPlayers || []);
    }
  }

  const  playCurrentWordAudio = async (word: currentWord) => {
    if (!matchId || !word.text) return;

    setIsAudioPlaying(true);

    try {
      const audioFinished = await playWordAudio(word.text, word.difficulty);

      console.log('Audio finished:', audioFinished);

      // If audio finished playing successfully, run the codeblock
      if (audioFinished) {
        if (matchId) {
          // Fetch current round from database
          const { data: matchData, error: matchError } = await supabase
            .from('matches')
            .select('current_round')
            .eq('id', matchId)
            .single();

          if (!matchError && matchData) {
            const round = matchData.current_round || 1;
            setCurrentRound(round);

            // Calculate dynamic timer duration
            const dynamicDuration = calculateTimerDuration(round);
            
            await initiateSpellingTimer(
              matchId,
              setCountdownActive,
              setTimeLeft,
              setTimeLeftPrecise,
              setInitialTime,
              dynamicDuration
            );

            setBreakTimerStart(false);
            setIsAudioPlaying(false);
            
            inputRef.current?.focus();
          }
        }
      }
    } catch (error) {
      console.error('Error in audio playback:', error);
    }
  };

  const playAudio = async (): Promise<currentWord> => {
    const wordData = await getCurrentWordForMatch(matchId);
    setClientCurrentWordId(wordData.id)
    setCorrectAnswer(wordData.text);
    setCurrentWord(wordData);
    await playCurrentWordAudio(wordData);

    return wordData
  }

  const startTimer = async () => {
    console.log(intervalRef.current)
    if (intervalRef.current) return;

    intervalRef.current = setInterval(async () => {
      setTimeLeftPrecise(prev => {
        const newTime = prev - 10; // Decrease by 10ms
        setTimeLeft(Math.ceil(newTime / 1000)); // Update seconds display
        return Math.max(0, newTime);
      });
    }, 10); // 10ms interval for precise timing

    return (() => {
      console.log("Timer Cleared");
      clearInterval(intervalRef.current!);
      intervalRef.current = null;
    });
  }

  useEffect(() => {
    // console.log("timeLeft", timeLeft, "timeLeftPrecise", timeLeftPrecise)// Only active players (not spectators)
    const activePlayers = playerData.filter(p => !p.is_spectator && (p.lives || 0) > 0);
    console.log(timeLeft)
    if (timeLeft <= 0) {
      console.log(activePlayers.length > 0, activePlayers.every(player => player.has_answered), matchId,!breakTimerStart);
    }
    if ((timeLeft <= 0 || activePlayers.length > 0 && activePlayers.every(player => player.has_answered)) && matchId && !breakTimerStart) {
      console.log("Timer Cleared");
      clearInterval(intervalRef.current!);
      intervalRef.current = null;

      (async () => {
        await updateMatchCurrentState(matchId, "spelling", "break");
        console.log("Updated from spelling to break")
        await updatePendingPlayers(matchId);
        await updatePlayersHasAnswered(matchId);
        await resetStartTime(matchId);

        await initiateSpellingTimer(
          matchId,
          setCountdownActive,
          setTimeLeft,
          setTimeLeftPrecise,
          setInitialTime,
          15
        );

        const pickedWordId = await setCurrentWordId(matchId, clientCurrentWordId);
        console.log(`Picked ID: ${pickedWordId}`)

        // Increment round number in database
        const { error: roundError } = await supabase
          .from('matches')
          .update({ current_round: currentRound + 1 })
          .eq('id', matchId);

        if (!roundError) {
          setCurrentRound(prev => prev + 1);
        }

        setClientGameState("break");
        setCountdownActive(false);
        setIsAudioPlaying(true);
        setBreakTimerStart(true);
      })();
    } // After break time
    else if (((timeLeft <= 0) || (activePlayers.length > 0 && activePlayers.every(player => player.has_answered))) && matchId && breakTimerStart) {
      console.log("Timer Cleared");
      clearInterval(intervalRef.current!);
      intervalRef.current = null;

      (async () => {
        await resetStartTime(matchId);
        await updateMatchCurrentState(matchId, "break", "spelling");
        console.log("Updated from break to spelling")
        await updateAllPlayersToPending(matchId);
        setClientGameState("spelling");
        await setMatchLock(matchId, false);
        setBreakTimerStart(false);
        await playAudio();
      })();
    }
  }, [timeLeft])

  useEffect(() => {
    console.log(!isAudioPlaying,!breakTimerStart)
    if (isAudioPlaying && breakTimerStart) { // Start timer for break time
      console.log("Break Timer Started")
      startTimer();
    } else if (!isAudioPlaying && !breakTimerStart) { // Start timer for spelling
      startTimer();
      console.log("Spelling Timer Started")
    }
  }, [breakTimerStart, isAudioPlaying])

  useEffect(() => {
    if (!matchId) return;
    const subscription = supabase
      .channel('match_changes')
      .on(
        'postgres_changes',
        { 
          event: 'UPDATE',
          schema: 'public',
          table: 'matches',
          filter: `id=eq.${matchId}`
        },
        async (payload) => {
          if (payload.new.current_state !== clientGameState) {
            setClientGameState(payload.new.current_state as GameStateType);
          }

          if (payload.new.current_word_id !== clientCurrentWordId) {
            try {
              const { data: wordData, error: wordError } = await supabase
                .from('words')
                .select('*')
                .eq('id', payload.new.current_word_id)
                .single();

              if (wordError) throw wordError;

              if (wordData) {
                setCurrentWord({
                  id: wordData.id,
                  text: wordData.text,
                  difficulty: wordData.difficulty,
                  audioUrl: wordData.audio_url
                });
              }
              setClientCurrentWordId(wordData.id);
            } catch (error) {
              console.error("Error fetching word data:", error);
            }
          }
        }
      )
      .subscribe();

    const playerSubscription = supabase
      .channel('match_players_changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'match_players',
          filter: `match_id=eq.${matchId}`
        },
        async (payload) => {
          // Check for elimination (lives went to 0)
          const oldPlayer = playerData.find(p => p.id === payload.new.player_id);
          const wasEliminated = oldPlayer && (oldPlayer.lives || 0) > 0 && payload.new.lives === 0;

          if (wasEliminated) {
            // Show elimination notification for spectators
            const currentPlayer = playerData.find(p => p.id === currentUser.id);
            const isSpectator = (currentPlayer?.lives || 0) <= 0 || currentPlayer?.is_spectator;

            if (isSpectator) {
              toast.info(`${oldPlayer.display_name} has been eliminated!`, {
                duration: 3000,
              });

              // Add to recent eliminations for spectator UI
              setRecentEliminations(prev => [
                ...prev.slice(-2), // Keep only last 2 eliminations
                oldPlayer.display_name || 'Player'
              ]);
            }
          }

          // Update the specific player in the playerData state
          setPlayerData(prevData =>
            prevData.map(player =>
              player.id === payload.new.player_id
                ? {
                    ...player,
                    score: payload.new.score,
                    lives: payload.new.lives,
                    lastAnswer: payload.new.lastAnswer,
                    has_answered: payload.new.has_answered
                  }
                : player
            )
          );
        }
      )
      .subscribe();

    // Subscribe to player deletions (when players leave)
    const playerDeletionSubscription = supabase
      .channel('match_players_deletions')
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'match_players',
          filter: `match_id=eq.${matchId}`
        },
        async (payload) => {
          // Player left the match, refresh player data
          console.log('Player left the match:', payload.old);

          // Show notification for spectators
          const currentPlayer = playerData.find(p => p.id === currentUser.id);
          const isSpectator = (currentPlayer?.lives || 0) <= 0 || currentPlayer?.is_spectator;

          if (isSpectator && payload.old) {
            const leftPlayer = playerData.find(p => p.id === payload.old.player_id);
            if (leftPlayer) {
              toast.info(`${leftPlayer.display_name} left the match`, {
                duration: 2000,
              });
            }
          }

          // Refresh player data to get accurate counts
          await fetchPlayers();
        }
      )
      .subscribe();

    const heartbeatInterval = setInterval(
      async () => { // Made the callback async
        if (!countdownActive) {
          const startTime = calculateTimeLeft(await performHeartbeatSyncBattle( // Await the function call
            supabase, 
            matchId, 
            countdownActive, 
            setCountdownActive, 
            setTimeLeft, 
            initialTime
          ))
          if (startTime <= 10 && startTime !== 0) {
            console.log("cleared", startTime)
            clearInterval(heartbeatInterval)
          }
        }
      }, 3000);

    return () => {
      subscription.unsubscribe();
      playerSubscription.unsubscribe();
      playerDeletionSubscription.unsubscribe();
      clearInterval(heartbeatInterval);
    };
  }, [matchId, supabase]);

  useEffect(() => {
    fetchPlayers();
  }, [])

  // First mount
  useEffect(() => {
    if (!matchId || playerData.length === 0) {
      console.log(matchId , playerData.length)
      if (playerData.length === 0) {
        fetchPlayers();
      }
      return;
    }
      
    (async () => {
      if (isFirstMount.current) {
        isFirstMount.current = false;
        console.log(playerData);

        // Run test calculations for development
        // testCalculations();
        const currentPlayer = playerData.find(player => player.id === currentUser.id);

        const { data: matchStateData, error} = await supabase
          .from('matches')
          .select("current_state, current_round")
          .eq('id', matchId)
          .single()

        if (error) {
          console.error(error)
        } else if (matchStateData) {
          console.log("First mount")
          // Initialize current round and timer
          const round = matchStateData.current_round || 1;
          setCurrentRound(round);
          setClientGameState(matchStateData.current_state as GameStateType);

          // If current state is spelling and current player hasn't answered, play audio
          if (matchStateData.current_state === "spelling" && currentPlayer?.has_answered === false) {
            console.log("PlayAudio()");
            await playAudio();
          }

          // If current state is spelling and current player has answered, set game state to break
          if (matchStateData.current_state === "spelling" && currentPlayer?.has_answered === true) {
            console.log("setClientGameState(break);")
            setClientGameState("break");

            await initiateSpellingTimer(
              matchId,
              setCountdownActive,
              setTimeLeft,
              setTimeLeftPrecise,
              setInitialTime,
              initialTime
            );

            setIsAudioPlaying(false);
            setBreakTimerStart(false);
            // await playAudio();
          }

          if (matchStateData.current_state === "break") {
            console.log("setClientGameState(break);")
            console.log("setBreakTimerStart(true);")
            await initiateSpellingTimer(
              matchId,
              setCountdownActive,
              setTimeLeft,
              setTimeLeftPrecise,
              setInitialTime,
              initialTime
            );

            setBreakTimerStart(true);
          }
        }

      }
    })();
  }, [fetchPlayers])

  return (
    <div className="bg-amber-50/30 min-h-screen w-full">
      <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8 flex flex-col gap-4 sm:gap-8">        
        {/* Header */}
        <header className="flex flex-col gap-3 sm:gap-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1 sm:gap-2">
              <Button
                variant="ghost"
                size="icon"
                className="text-amber-800 hover:text-amber-900 hover:bg-amber-100 h-8 w-8 sm:h-9 sm:w-9"
                onClick={() => handleLeaveRoom(currentUser.id)}
              >
                <ArrowLeft size={18} />
              </Button>
              <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-amber-900">
                {roomName} Battle
              </h1>
            </div>
            <div className={cn("px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm", roomBgColor)}>
              <span className={cn("font-medium", roomColor)}>{roomName}</span>
            </div>
          </div>
        </header>

        {/* Timer */}
        <div className="bg-amber-100 text-sm p-3 px-4 rounded-lg text-amber-800 flex flex-col gap-2 border border-amber-200 sticky top-[10px] z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Clock size={14} />
              <span>
                {(clientGameState === "spelling" && isAudioPlaying) && `Listening...`}
                {(clientGameState === "spelling" && !isAudioPlaying) && `Time left: ${formatTime(timeLeft)} (${initialTime}s timer)`}
                {clientGameState === "break" && "Break time!"}
                {clientGameState === "gameOver" && "Game Over!"}
                {clientGameState === "results" && "Final Results"}
              </span>
            </div>
            <span>Round {currentRound}</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6">
          {/* Main game area - conditionally rendered based on clientGameState */}
          <div className="md:col-span-2 bg-white rounded-xl p-6 border shadow-sm">
            {clientGameState === "spelling" && (() => {
              const currentPlayer = playerData.find(p => p.id === currentUser.id);
              const isSpectator = (currentPlayer?.lives || 0) <= 0 || currentPlayer?.is_spectator;

              if (isSpectator) {
                // Spectator UI for spelling state
                return (
                  <div className="flex flex-col items-center justify-center py-8">
                    {/* Spectator Badge */}
                    <div className="mb-4 px-4 py-2 bg-gray-100 border border-gray-300 rounded-full">
                      <span className="text-sm font-medium text-gray-700">👁️ SPECTATING</span>
                    </div>

                    <h2 className="text-xl font-semibold text-amber-900 mb-6">
                      Players are spelling the word
                    </h2>

                    <div className="flex flex-col items-center gap-2 mb-8">
                      <div className="flex items-center gap-2">
                        {isAudioPlaying ? (
                          <div className="text-center">
                            <div className="h-12 w-48 bg-amber-100 animate-pulse rounded-lg mb-2"></div>
                            <p className="text-sm text-amber-600">Word is being played...</p>
                          </div>
                        ) : (
                          <>
                            <div className="text-4xl font-bold text-amber-800">
                              ??????
                            </div>
                          </>
                        )}
                      </div>

                      {isAudioPlaying ? (
                        <div className="h-6 w-24 bg-amber-100 animate-pulse rounded-full mt-2"></div>
                      ) : (
                        <div className={cn(
                          "text-xs px-2 py-1 rounded-full mt-2",
                          currentWord.difficulty === "easy" ? "bg-green-100 text-green-800" :
                          currentWord.difficulty === "medium" ? "bg-blue-100 text-blue-800" :
                          "bg-amber-100 text-amber-800"
                        )}>
                          {currentWord.difficulty.charAt(0).toUpperCase() + currentWord.difficulty.slice(1)} difficulty
                        </div>
                      )}
                    </div>

                    {/* Timer Progress Bar */}
                    <div className="w-full max-w-md mb-6">
                      <div className="text-center mb-3 py-2 px-4 rounded-md text-sm font-medium bg-blue-100 text-blue-800">
                        {isAudioPlaying ?
                          "Players are listening to the word..." :
                          `Players have ${formatTime(timeLeft)} to spell the word`
                        }
                      </div>

                      <div className="w-full mb-3 h-2 bg-amber-200 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-amber-600 transition-all duration-100"
                          style={{ width: `${(timeLeftPrecise / (initialTime * 1000)) * 100}%` }}
                        />
                      </div>
                    </div>

                    {/* Active Players Progress */}
                    <div className="w-full max-w-md bg-amber-50 rounded-xl p-4 border border-amber-200 shadow-sm">
                      <h3 className="text-lg font-semibold text-amber-900 mb-3 text-center">
                        Active Players Progress
                      </h3>
                      <div className="space-y-2">
                        {playerData.filter(p => (p.lives || 0) > 0 && !p.is_spectator).map((player) => (
                          <div
                            key={player.id}
                            className="flex items-center justify-between p-2 rounded-lg bg-white border border-amber-100"
                          >
                            <div className="flex items-center gap-2">
                              <Avatar className="h-8 w-8">
                                <AvatarFallback className="bg-amber-200 text-amber-800 text-xs">
                                  {player.display_name?.split(' ').map((word: string) => word.charAt(0).toUpperCase()).join('').substring(0, 2) || 'AN'}
                                </AvatarFallback>
                              </Avatar>
                              <span className="font-medium text-amber-900 text-sm">
                                {player.display_name}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="flex">
                                {Array.from({ length: player.lives || 0 }).map((_, i) => (
                                  <Heart key={i} size={12} className="text-red-500" fill="#ef4444" />
                                ))}
                              </div>
                              <span className={cn(
                                "text-xs px-2 py-1 rounded-full font-medium",
                                player.has_answered
                                  ? "bg-green-100 text-green-800"
                                  : "bg-yellow-100 text-yellow-800"
                              )}>
                                {player.has_answered ? "✓ Answered" : "⏳ Thinking..."}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Recent Eliminations for Spectators */}
                    {recentEliminations.length > 0 && (
                      <div className="w-full max-w-md bg-red-50 rounded-xl p-4 border border-red-200 shadow-sm mt-4">
                        <h3 className="text-lg font-semibold text-red-900 mb-3 text-center">
                          Recent Eliminations
                        </h3>
                        <div className="space-y-1">
                          {recentEliminations.slice(-3).map((playerName, index) => (
                            <div key={index} className="text-sm text-red-800 text-center">
                              💀 {playerName} was eliminated
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                );
              } else {
                // Active player UI for spelling state
                return (
                  <div className="flex flex-col items-center justify-center py-8">
                    <h2 className="text-xl font-semibold text-amber-900 mb-6">
                      Spell the word:
                    </h2>

                    <div className="flex flex-col items-center gap-2 mb-8">
                      <div className="flex items-center gap-2">
                        {isAudioPlaying ? (
                          <div className="text-center">
                            <div className="h-12 w-48 bg-amber-100 animate-pulse rounded-lg mb-2"></div>
                            <p className="text-sm text-amber-600">Loading word...</p>
                          </div>
                        ) : (
                          <>
                            <div className="text-4xl font-bold text-amber-800">
                              ??????
                            </div>
                          </>
                        )}
                      </div>

                      {isAudioPlaying ? (
                        <div className="h-6 w-24 bg-amber-100 animate-pulse rounded-full mt-2"></div>
                      ) : (
                        <div className={cn(
                          "text-xs px-2 py-1 rounded-full mt-2",
                          currentWord.difficulty === "easy" ? "bg-green-100 text-green-800" :
                          currentWord.difficulty === "medium" ? "bg-blue-100 text-blue-800" :
                          "bg-amber-100 text-amber-800"
                        )}>
                          {currentWord.difficulty.charAt(0).toUpperCase() + currentWord.difficulty.slice(1)} difficulty
                        </div>
                      )}
                    </div>

                    <div className="w-full max-w-md">
                      <div className={cn(
                        "text-center mb-3 py-2 px-4 rounded-md text-sm font-medium",
                        isAudioPlaying ? "bg-yellow-100 text-yellow-800" : "bg-green-100 text-green-800"
                      )}>
                        {isAudioPlaying ?
                          "Now listen to the word!" :
                          "Now spell the word!"
                        }
                      </div>

                      <div className="w-full mb-3 h-1 bg-amber-200 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-amber-600 transition-all duration-100"
                          style={{ width: `${(timeLeftPrecise / (initialTime * 1000)) * 100}%` }}
                        />
                      </div>
                      <form
                        className="flex gap-2"
                        onSubmit={(e) => {
                          e.preventDefault();
                          handleCheckAnswer();
                        }}
                      >
                        <input
                          ref={inputRef}
                          type="text"
                          className="flex h-10 w-full rounded-md border border-amber-300 bg-white px-3 py-2 text-sm placeholder:text-amber-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-amber-400 focus-visible:ring-offset-2"
                          placeholder="Type the word here..."
                          value={userAnswer}
                          onChange={(e) => setUserAnswer(e.target.value)}
                        />
                        <Button
                          className="bg-amber-600 hover:bg-amber-700 text-white"
                          type="submit"
                          disabled={isAudioPlaying}
                        >
                          Submit
                        </Button>
                      </form>
                    </div>

                    <div className="mt-6 flex justify-center">
                      <div className="flex gap-2">
                        {(() => {
                          const lives = currentPlayer?.lives || 0;
                          return Array.from({ length: lives }).map((_, i) => (
                            <Heart key={i} size={24} className="text-red-500" fill="#ef4444" />
                          ));
                        })()}
                      </div>
                    </div>
                  </div>
                );
              }
            })()}

            {clientGameState === "break" && (() => {
              const currentPlayer = playerData.find(p => p.id === currentUser.id);
              const isSpectator = (currentPlayer?.lives || 0) <= 0 || currentPlayer?.is_spectator;

              return (
                <div className="flex flex-col items-center justify-center py-8">
                  {/* Spectator badge for break time */}
                  {isSpectator && (
                    <div className="mb-4 px-4 py-2 bg-gray-100 border border-gray-300 rounded-full">
                      <span className="text-sm font-medium text-gray-700">👁️ SPECTATING</span>
                    </div>
                  )}

                  <div className="text-center mb-4">
                    { !breakTimerStart ? (
                      <div className="text-4xl font-bold text-amber-600 mb-2">
                        Break Time!
                      </div>
                    ) : (
                      <div className="text-4xl font-bold text-amber-600 mb-2">
                        {formatTime(timeLeft)}
                      </div>
                    )}
                    { !breakTimerStart ? (
                      <p className="text-amber-700">
                        {isSpectator ? "Watch the next round unfold..." : "Next round starting soon..."}
                      </p>
                    ) : (
                      <p className="text-amber-700">
                        {isSpectator ? "Next round is about to begin!" : "Get ready for the next round!"}
                      </p>
                    )}
                  </div>

                  {/* Current standings */}
                  <div className="w-full max-w-md bg-amber-50 rounded-xl p-4 border border-amber-200 shadow-sm mb-4">
                    <h3 className="text-lg font-semibold text-amber-900 mb-3 text-center">Current Standings</h3>
                    <div className="grid grid-cols-3 gap-4 items-center mb-2 pb-2 border-b border-amber-200">
                      <span className="font-medium text-amber-800">Player</span>
                      <span className="font-medium text-amber-800 text-center">Status</span>
                      <span className="font-medium text-amber-800 text-right">Score</span>
                    </div>

                    {[...playerData].sort((a, b) => (b.score || 0) - (a.score || 0)).map((player) => (
                      <div
                        key={player.id}
                        className={cn(
                          "grid grid-cols-3 gap-4 items-center py-2 px-2 rounded-lg border-b border-amber-100",
                          ((player.lives || 0) <= 0 || player.is_spectator) && "bg-gray-100"
                        )}
                      >
                        <div className="flex items-center gap-2">
                          <Avatar className="h-7 w-7">
                            <AvatarFallback className={cn("text-xs", "bg-amber-200 text-amber-800")}>
                              {player.display_name?.split(' ').map((word: string) => word.charAt(0).toUpperCase()).join('').substring(0, 2) || 'AN'}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium text-sm text-amber-800">
                              {player.display_name}
                              {((player.lives || 0) <= 0 || player.is_spectator) && (
                                <span className="text-xs text-gray-500 ml-1">(Eliminated)</span>
                              )}
                            </div>
                            <div className="flex mt-1">
                              {Array.from({ length: player.lives || 0 }).map((_, i) => (
                                <Heart key={i} size={14} className="text-red-500" fill="#ef4444" />
                              ))}
                            </div>
                          </div>
                        </div>
                        <div className="flex justify-center">
                          <span className={cn(
                            "text-xs px-2 py-1 rounded-full",
                            player.lastAnswer === "correct" ? "bg-green-100 text-green-800" :
                            player.lastAnswer === "incorrect" ? "bg-red-100 text-red-800" :
                            "bg-amber-100 text-amber-800",
                            ((player.lives || 0) <= 0 || player.is_spectator) && "hidden" // Hide status for spectators
                          )}>
                            {player.lastAnswer}
                          </span>
                        </div>
                        <div className="font-medium text-amber-800 text-right">
                          {player.score || 0}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Enhanced spectator information */}
                  {isSpectator && (() => {
                    const stats = getRoundStats();
                    return (
                      <div className="w-full max-w-md space-y-4">
                        {/* Match Statistics */}
                        <div className="bg-blue-50 rounded-xl p-4 border border-blue-200 shadow-sm">
                          <h3 className="text-lg font-semibold text-blue-900 mb-3 text-center">Match Statistics</h3>
                          <div className="grid grid-cols-2 gap-3 text-sm">
                            <div className="bg-white rounded-lg p-2 text-center">
                              <div className="font-bold text-blue-800">{stats.activePlayers}</div>
                              <div className="text-blue-600">Active Players</div>
                            </div>
                            <div className="bg-white rounded-lg p-2 text-center">
                              <div className="font-bold text-blue-800">{stats.spectators}</div>
                              <div className="text-blue-600">Spectators</div>
                            </div>
                            <div className="bg-white rounded-lg p-2 text-center">
                              <div className="font-bold text-green-800">{stats.correctAnswers}</div>
                              <div className="text-green-600">Correct Answers</div>
                            </div>
                            <div className="bg-white rounded-lg p-2 text-center">
                              <div className="font-bold text-red-800">{stats.incorrectAnswers}</div>
                              <div className="text-red-600">Wrong Answers</div>
                            </div>
                          </div>
                        </div>

                        {/* Round Information */}
                        <div className="bg-amber-50 rounded-xl p-4 border border-amber-200 shadow-sm">
                          <h3 className="text-lg font-semibold text-amber-900 mb-3 text-center">Round Info</h3>
                          <div className="text-sm text-amber-800 space-y-2">
                            <div className="flex justify-between">
                              <span>Current Round:</span>
                              <span className="font-medium">#{currentRound}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Difficulty:</span>
                              <span className="font-medium capitalize">{currentWord.difficulty}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Highest Score:</span>
                              <span className="font-medium">{stats.highestScore} pts</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Average Score:</span>
                              <span className="font-medium">{stats.averageScore} pts</span>
                            </div>
                          </div>
                        </div>

                        {/* Spectator Tips */}
                        <div className="bg-gray-50 rounded-xl p-4 border border-gray-200 shadow-sm">
                          <h3 className="text-lg font-semibold text-gray-900 mb-3 text-center">Spectator Mode</h3>
                          <div className="text-sm text-gray-700 space-y-1">
                            <p>👁️ You can watch the match until it ends</p>
                            <p>🔔 You'll get notifications when players are eliminated</p>
                            <p>📊 Track live progress during spelling rounds</p>
                            <p>🏆 See final rankings when the match concludes</p>
                          </div>
                        </div>
                      </div>
                    );
                  })()}
                </div>
              );
            })()}

            {clientGameState === "gameOver" && (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="text-center mb-6">
                  <div className="text-4xl font-bold text-red-600 mb-2">
                    Game Over!
                  </div>
                  <p className="text-amber-700">The battle has ended</p>
                </div>

                <div className="w-full max-w-md bg-amber-50 rounded-xl p-4 border border-amber-200 shadow-sm mb-4">
                  <h3 className="text-lg font-semibold text-amber-900 mb-3 text-center">Final Scores</h3>
                  <div className="grid grid-cols-3 gap-4 items-center mb-2 pb-2 border-b border-amber-200">
                    <span className="font-medium text-amber-800">Player</span>
                    <span className="font-medium text-amber-800 text-center">Status</span>
                    <span className="font-medium text-amber-800 text-right">Score</span>
                  </div>

                  {playerData.map((player) => (
                    <div
                      key={player.id}
                      className={cn(
                        "grid grid-cols-3 gap-4 items-center py-2 px-2 rounded-lg border-b border-amber-100",
                        ((player.lives || 0) <= 0 || player.is_spectator) && "bg-gray-100"
                      )}
                    >
                      <div className="flex items-center gap-2">
                        <Avatar className="h-7 w-7">
                          <AvatarFallback className={cn("text-xs", "bg-amber-200 text-amber-800")}>
                            {player.display_name?.split(' ').map((word: string) => word.charAt(0).toUpperCase()).join('').substring(0, 2) || 'AN'}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium text-sm text-amber-800">
                            {player.display_name}
                          </div>
                          <div className="flex mt-1">
                            {Array.from({ length: player.lives || 0 }).map((_, i) => (
                              <Heart key={i} size={14} className="text-red-500" fill="#ef4444" />
                            ))}
                          </div>
                        </div>
                      </div>
                      <div className="flex justify-center">
                        <span className={cn(
                          "text-xs px-2 py-1 rounded-full",
                          player.lastAnswer === "correct" ? "bg-green-100 text-green-800" :
                          player.lastAnswer === "incorrect" ? "bg-red-100 text-red-800" :
                          "bg-amber-100 text-amber-800",
                          ((player.lives || 0) <= 0 || player.is_spectator) && "hidden" // Hide status for spectators
                        )}>
                          {player.lastAnswer}
                        </span>
                      </div>
                      <div className="font-medium text-amber-800 text-right">
                        {player.score || 0}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {clientGameState === "results" && (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="text-center mb-6">
                  <div className="text-4xl font-bold text-green-600 mb-2">
                    Battle Results
                  </div>
                  <p className="text-amber-700">Final standings</p>
                </div>

                <div className="w-full max-w-md bg-amber-50 rounded-xl p-4 border border-amber-200 shadow-sm">
                  <h3 className="text-lg font-semibold text-amber-900 mb-3 text-center">Rankings</h3>
                  <div className="grid grid-cols-3 gap-4 items-center mb-2 pb-2 border-b border-amber-200">
                    <span className="font-medium text-amber-800">Player</span>
                    <span className="font-medium text-amber-800 text-center">Status</span>
                    <span className="font-medium text-amber-800 text-right">Score</span>
                  </div>

                  {[...playerData]
                    .sort((a, b) => (b.score || 0) - (a.score || 0))
                    .map((player, index) => (
                      <div
                        key={player.id}
                        className={cn(
                          "grid grid-cols-3 gap-4 items-center py-2 px-2 rounded-lg border-b border-amber-100",
                          ((player.lives || 0) <= 0 || player.is_spectator) && "bg-gray-100"
                        )}
                      >
                        <div className="flex items-center gap-2">
                          <div className="w-6 h-6 flex items-center justify-center bg-amber-200 text-amber-800 rounded-full text-xs font-bold">
                            {index + 1}
                          </div>
                          <div>
                            <div className="font-medium text-sm text-amber-800">
                              {player.display_name}
                            </div>
                            <div className="flex mt-1">
                              {Array.from({ length: player.lives || 0 }).map((_, i) => (
                                <Heart key={i} size={14} className="text-red-500" fill="#ef4444" />
                              ))}
                            </div>
                          </div>
                        </div>
                        <div className="flex justify-center">
                          <span className={cn(
                            "text-xs px-2 py-1 rounded-full",
                            player.lastAnswer === "correct" ? "bg-green-100 text-green-800" :
                            player.lastAnswer === "incorrect" ? "bg-red-100 text-red-800" :
                            "bg-amber-100 text-amber-800",
                            ((player.lives || 0) <= 0 || player.is_spectator) && "hidden" // Hide status for spectators
                          )}>
                            {player.lastAnswer}
                          </span>
                        </div>
                        <div className="font-medium text-amber-800 text-right">
                          {player.score || 0}
                        </div>
                      </div>
                    ))}
                </div>

                <Button className="mt-6 bg-amber-600 hover:bg-amber-700 text-white">
                  Play Again
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Game info section */}
        <div className="bg-white rounded-xl p-6 border shadow-sm">
          <h2 className="text-xl font-semibold text-amber-900 mb-4">
            Battle Information
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
              <h3 className="font-medium text-amber-800 mb-2 text-base">Rules</h3>
              <ul className="text-sm text-amber-700 list-disc pl-4 space-y-1">
                <li>Type the word correctly within 15 seconds</li>
                <li>Each correct answer earns you 10 points</li>
                <li>Three mistakes and you're eliminated</li>
              </ul>
            </div>
            <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
              <h3 className="font-medium text-amber-800 mb-2 text-base">Tips</h3>
              <ul className="text-sm text-amber-700 list-disc pl-4 space-y-1">
                <li>Listen carefully to the pronunciation</li>
                <li>Watch for common spelling patterns</li>
                <li>Double-check before submitting</li>
              </ul>
            </div>
            <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
              <h3 className="font-medium text-amber-800 mb-2 text-base">
                Difficulty: {roomName}
              </h3>
              <p className="text-sm text-amber-700">
                {roomName === "Easy" && "Simple words with common spelling patterns"}
                {roomName === "Medium" && "Moderate difficulty with some tricky words"}
                {roomName === "Hard" && "Challenging words that test your skills"}
              </p>
            </div>
          </div>
        </div>


      </div>
    </div>
  );
}
