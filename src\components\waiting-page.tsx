"use client";

import { useState, useEffect, Dispatch, SetStateAction } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Clock, Users, ArrowLeft, Trophy, Loader2, Volume2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { createClient } from "../../supabase/client";
import { AudioPreloader } from "@/components/audio-preloader";
import {
  WaitingUser,
  LeaderboardUser,
  checkAndRegisterUser,
  fetchWaitingUsers,
  performHeartbeatSync,
  calculateTimeLeft,
  formatTime,
  leaveRoom,
  resetStartTime,
} from "../utils/waiting-utils";
import { updateMatchCurrentState, setCurrentWordId, setMatchLock, updateMatchState } from "@/utils/database";
import { GameStateType, Match, User } from "@/interfaces/interfaces"; // Import the User interface

interface WaitingPageProps {
  roomName: string;
  roomColor: string;
  roomBgColor: string;
  initialTimeInSeconds?: number;
  matchId?: string;
  onLeaveRoom?: () => void;
  clientGameState: GameStateType;
  setClientGameState: Dispatch<SetStateAction<GameStateType>>;
  clientCurrentWordId: number;
  setClientCurrentWordId: Dispatch<SetStateAction<number>>;
  currentUser: User;
}

export default function WaitingPage({
  roomName,
  roomColor,
  roomBgColor,
  initialTimeInSeconds = 15,
  matchId,
  clientGameState,
  setClientGameState,
  onLeaveRoom,
  clientCurrentWordId,
  setClientCurrentWordId,
  currentUser,
}: WaitingPageProps) {
  const router = useRouter();
  const [supabase] = useState(() => createClient());
  const [timeLeft, setTimeLeft] = useState(initialTimeInSeconds);
  const [isLeaving, setIsLeaving] = useState(false);
  const [isCheckingRegistration, setIsCheckingRegistration] = useState(true);
  const [registrationError, setRegistrationError] = useState<string | null>(null);
  const [waitingUsers, setWaitingUsers] = useState<WaitingUser[]>([]);
  const [leaderboardUsers, setLeaderboardUsers] = useState<LeaderboardUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentMatchId, setCurrentMatchId] = useState<string | null>(null);
  const [countdownActive, setCountdownActive] = useState(false);
  const [playerCount, setPlayerCount] = useState(0);
  const [isRegistered, setIsRegistered] = useState(false);

  const [hasExecutedEndState, setHasExecutedEndState] = useState(false);


  const handleLeaveRoom = () => {
    leaveRoom(roomName, onLeaveRoom, router, setIsLeaving);
  };

  useEffect(() => {
    fetchWaitingUsers(
      supabase,
      roomName,
      setWaitingUsers,
      setPlayerCount,
      setCountdownActive,
      setCurrentMatchId,
      setTimeLeft,
      initialTimeInSeconds
    );

    const playersChannel = supabase
      .channel('waiting-room-players')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'match_players'
        },
        () => {
          fetchWaitingUsers(
            supabase,
            roomName,
            setWaitingUsers,
            setPlayerCount,
            setCountdownActive,
            setCurrentMatchId,
            setTimeLeft,
            initialTimeInSeconds
          );
        }
      )
      .subscribe();

    const matchesChannel = supabase
      .channel('waiting-room-matches')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'matches'
        },
        () => {
          fetchWaitingUsers(
            supabase,
            roomName,
            setWaitingUsers,
            setPlayerCount,
            setCountdownActive,
            setCurrentMatchId,
            setTimeLeft,
            initialTimeInSeconds
          );
        }
      )
      .subscribe();

    const matchStateChannel = supabase
      .channel('match_state_changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'matches',
          filter: `id=eq.${matchId}`
        },
        async (payload) => {
          if (payload.new.current_state !== clientGameState) {
            console.log("Game state updated from server:", payload.new.current_state);
            setClientGameState(payload.new.current_state as GameStateType);
          }
        }
      )
      .subscribe();

    const heartbeatInterval = setInterval(
      () => performHeartbeatSync(
        supabase,
        currentMatchId,
        countdownActive,
        setCountdownActive,
        setTimeLeft,
        initialTimeInSeconds,
        router,
        roomName
      ),
      2000
    );

    return () => {
      supabase.removeChannel(playersChannel);
      supabase.removeChannel(matchesChannel);
      supabase.removeChannel(matchStateChannel);
      clearInterval(heartbeatInterval);
    };
  }, [roomName, supabase, currentMatchId, countdownActive, initialTimeInSeconds, router]);

  useEffect(() => {
    const checkRegistration = async () => {
      await checkAndRegisterUser(
        supabase,
        roomName,
        router,
        setIsCheckingRegistration,
        setRegistrationError,
        setIsRegistered,
        setClientGameState,
      );
    };

    checkRegistration();
  }, [roomName]);

  useEffect(() => {
    let timer: NodeJS.Timeout | undefined;

    if (!currentMatchId || !countdownActive) {
      return;
    }

    timer = setInterval(async () => {
      try {
        const { data, error } = await supabase
          .from('matches')
          .select('start_time, status')
          .eq('id', currentMatchId)
          .single();

        if (error || !data || !data.start_time) {
          console.error("Error fetching match for countdown:", error);
          clearInterval(timer);
          setCountdownActive(false);
          return;
        }

        if (data.status === 'ongoing') {
          clearInterval(timer);
          router.replace(`/battle/${roomName.toLowerCase()}`);
          return;
        }

        const newTimeLeft = calculateTimeLeft(data.start_time);
        setTimeLeft(newTimeLeft);

        if (newTimeLeft <= 0 && !hasExecutedEndState) {
          console.clear();
          setHasExecutedEndState(true);
          await updateMatchCurrentState(currentMatchId, "waiting", "spelling");
          await updateMatchState(currentMatchId, "waiting", "ongoing");
          await setMatchLock(currentMatchId, false);
          await resetStartTime(currentMatchId);
          setClientGameState("spelling");
          clearInterval(timer);
        }
      } catch (error) {
        console.error("Error in countdown timer:", error);
      }
    }, 1000);

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [currentMatchId, countdownActive, supabase, router, roomName, hasExecutedEndState]);

  // Set the current match ID if not yet provided
  useEffect(() => {
    const setWordId = async () => {
      if (currentMatchId) {
        const pickedWordId = await setCurrentWordId(currentMatchId, clientCurrentWordId);
        console.log(`Picked ID: ${pickedWordId}`);
        setClientCurrentWordId(pickedWordId);
      }
    };
    setWordId();
  }, [currentMatchId, clientCurrentWordId, roomName, supabase]);

  if (isCheckingRegistration) {
    return (
      <div className="bg-amber-50/30 min-h-screen w-full flex flex-col items-center justify-center gap-6">
        {isCheckingRegistration && (
          <div className="bg-white p-8 rounded-xl shadow-md flex flex-col items-center gap-4">
            <Loader2 className="h-12 w-12 animate-spin text-amber-600" />
            <h2 className="text-xl font-semibold text-amber-900">Checking room registration...</h2>
            <p className="text-amber-600">Please wait while we prepare your room</p>
          </div>
        )}
      </div>
    );
  }

  if (registrationError) {
    const isAlreadyInRoomError = registrationError.includes("You are already in the");
    const isNotRegisteredError = registrationError.includes("You are not registered in this room");
    const isDirectAccessError = isNotRegisteredError;

    let currentRoomName = "";
    if (isAlreadyInRoomError) {
      const match = registrationError.match(/in the (\w+) room/);
      if (match && match[1]) {
        currentRoomName = match[1].toLowerCase();
      }
    }

    return (
      <div className="bg-amber-50/30 min-h-screen w-full flex items-center justify-center">
        <div className="bg-white p-8 rounded-xl shadow-md flex flex-col items-center gap-4 max-w-md">
          <div className={`${isDirectAccessError ? 'bg-amber-100' : 'bg-red-100'} p-3 rounded-full`}>
            <svg xmlns="http://www.w3.org/2000/svg" className={`h-8 w-8 ${isDirectAccessError ? 'text-amber-500' : 'text-red-500'}`} fill="none" viewBox="0 0 极狐24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-amber-900">
            {isDirectAccessError ? "Access Denied" : "Unable to join room"}
          </h2>
          <p className="text-amber-600 text-center">{registrationError}</p>

          {isDirectAccessError && (
            <p className="text-amber-600 text-center">
              Please join this room from the dashboard first.
            </p>
          )}

          <div className="flex flex-col sm:flex-row gap-3 mt-4">
            {isAlreadyInRoomError && currentRoomName && (
              <Button
                className="bg-green-600 hover:bg-green-700 text-white"
                onClick={() => router.replace(`/dashboard/${currentRoomName}`)}
              >
                Go to {currentRoomName.charAt(0).toUpperCase() + currentRoomName.slice(1)} Room
              </Button>
            )}
            <Button
              className="bg-amber-600 hover:bg-amber-700 text-white"
              onClick={() => window.location.href = "/dashboard"}
            >
              Return to Dashboard
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-amber-50/30 min-h-screen w-full">
      <div className="container mx-auto px-4 py-8 flex flex-col gap-8">
        <header className="flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={handleLeaveRoom}
                disabled={isLeaving}
                className="text-amber-800 hover:text-amber-900 hover:bg-amber-100"
              >
                {isLeaving ? (
                  <Loader2 size={20} className="animate-spin" />
                ) : (
                  <ArrowLeft size={20} />
                )}
              </Button>
              <h1 className="text-3xl font-bold text-amber-900">
                Waiting Room: {roomName}
              </h1>
            </div>
            <div className={cn("px-3 py-1 rounded-full", roomBgColor)}>
              <span className={cn("font-medium", roomColor)}>{roomName}</span>
            </div>
          </div>
        </header>
        <div className="bg-amber-100 text-sm p-3 px-4 rounded-lg text-amber-800 flex gap-2 items-center border border-amber-200 sticky top-[10px] z-10">
          <Clock size={14} />
          <span>
            {playerCount < 2
              ? "Waiting for more players to join..."
              : !countdownActive
                ? "Synchronizing with other players..."
                : timeLeft > 0
                  ? `Game starts in ${formatTime(timeLeft)}`
                  : "Game starting now!"}
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 bg-white rounded-xl p-6 border shadow-sm">
            <h2 className="text-xl font-semibold text-amber-900 mb-4 flex items-center gap-2">
              <Clock className="h-5 w-5 text-amber-600" />
              Countdown to Battle
            </h2>
            <div className="flex flex-col items-center justify-center py-12">
              {playerCount < 2 ? (
                <>
                  <div className="text-4xl font-bold text-amber-800 mb-4">
                    Waiting for players...
                  </div>
                  <p className="text-amber-600">
                    The countdown will start when at least 2 players join.
                  </p>
                </>
              ) : !countdownActive ? (
                <>
                  <div className="text-4xl font-bold text-amber-800 mb-4">
                    Synchronizing...
                  </div>
                  <p className="text-amber-600">
                    Syncing with other players to start the countdown.
                  </p>
                </>
              ) : (
                <>
                  <div className="text-7xl font-bold text-amber-800 mb-4">
                    {formatTime(timeLeft)}
                  </div>
                  <p className="text-amber-600">
                    {timeLeft > 0
                      ? "Get ready for the spelling battle!"
                      : "Let the battle begin!"}
                  </p>
                  {/* {timeLeft <= 0 && (
                    <Button
                      className="mt-6 bg-amber-600 hover:bg-amber-700 text-white"
                      onClick={() =>
                        router.replace(`/battle/${roomName.toLowerCase()}`)
                      }
                    >
                      Enter Battle
                    </Button>
                  )} */}
                </>
              )}
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 border shadow-sm">
            <h2 className="text-xl font-semibold text-amber-900 mb-4 flex items-center gap-2">
              <Users className="h-5 w-5 text-amber-600" />
              Players ({waitingUsers.length})
            </h2>
            <div className="flex flex-col gap-3">
              {waitingUsers.map((user) => (
                <div
                  key={user.id}
                  className="flex items-center gap-3 p-2 rounded-lg hover:bg-amber-50"
                >
                  <Avatar>
                    {user.avatar ? (
                      <AvatarImage src={user.avatar} alt={user.name} />
                    ) : null}
                    <AvatarFallback className="bg-amber-200 text-amber-800">
                      {user.initials}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium text-amber-900">{user.name}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border shadow-sm">
          <h2 className="text-xl font-semibold text-amber-900 mb-4 flex items-center gap-2">
            <Trophy className="h-5 w-5 text-amber-600" />
            {roomName} Leaderboard - Top 20 Players
          </h2>

          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-700"></div>
            </div>
          ) : leaderboardUsers.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-left border-b border-amber-200">
                    <th className="pb-2 text-amber-800 font-medium">Rank</th>
                    <th className="pb-2 text-amber-800 font-medium">Player</th>
                    <th className="pb-2 text-amber-800 font-medium text-right">
                      Score
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {leaderboardUsers.map((user) => (
                    <tr
                      key={user.id}
                      className="border-b border-amber-100 last:border-0"
                    >
                      <td className="py-3 text-amber-900 font-medium">
                        {user.rank <= 3 ? (
                          <span
                            className={cn(
                              "inline-flex items-center justify-center w-6 h-6 rounded-full text-white font-bold",
                              user.rank === 1
                                ? "bg-yellow-500"
                                : user.rank === 2
                                  ? "bg-gray-400"
                                  : "bg-amber-700",
                            )}
                          >
                            {user.rank}
                          </span>
                        ) : (
                          user.rank
                        )}
                      </td>
                      <td className="py-3">
                        <div className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            {user.avatar_url ? (
                              <AvatarImage
                                src={user.avatar_url}
                                alt={user.name || ""}
                              />
                            ) : null}
                            <AvatarFallback className="bg-amber-200 text-amber-800 text-xs">
                              {user.name?.substring(0, 2).toUpperCase() || "AN"}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-amber-900">{user.name}</span>
                        </div>
                      </td>
                      <td className="py-3 text-right font-medium text-amber-900">
                        {user.score.toLocaleString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 text-amber-700">
              No scores available for this difficulty level yet.
            </div>
          )}
        </div>

        <div className="bg-white rounded-xl p-6 border shadow-sm">
          <h2 className="text-xl font-semibold text-amber-900 mb-4">
            Game Information
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
              <h3 className="font-medium text-amber-800 mb-2">Rules</h3>
              <ul className="text-sm text-amber-700 list-disc pl-4 space-y-1">
                <li>Type the word correctly within 15 seconds</li>
                <li>Each correct answer earns you points</li>
                <li>Three mistakes and you're out</li>
              </ul>
            </div>
            <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
              <h3 className="font-medium text-amber-800 mb-2">Power-Ups</h3>
                <ul className="text-sm text-amber-700 list-disc pl-4 space-y-1">
                <li>Time Freeze - Extra seconds to answer</li>
                <li>Double Points - Double your score</li>
                <li>Word Skip - Skip a difficult word</li>
              </ul>
            </div>
            <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
              <h3 className="font-medium text-amber-800 mb-2">
                Difficulty: {roomName}
              </h3>
              <p className="text-sm text-amber-700">
                {roomName === "Easy" &&
                  "Simple words with common spelling patterns"}
                {roomName === "Medium" &&
                  "Moderate difficulty with some tricky words"}
                {roomName === "Hard" &&
                  "Challenging words that test your skills"}
                {roomName === "Extreme" &&
                  "Expert level with the hardest words"}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
